{"functions": [{"source": "functions", "codebase": "pickle-glass", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "pickleglass_web/out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}}